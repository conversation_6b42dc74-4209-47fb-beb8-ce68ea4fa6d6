{"system": "人工智能图像生成器", "helloWorld": "你好，世界！", "Describe the image you want to generate...": "描述您想要生成的图像...", "appTitle": "人工智能图像创作", "copyright": "版权所有 © {year}，AI图像创作", "available": "可用于新项目", "notAvailable": "目前不可用", "blog": "博客", "copyLink": "复制链接", "minRead": "分钟阅读", "articleLinkCopied": "文章链接已复制到剪贴板", "clickToClose": "单击任意位置或按ESC关闭", "promptDetails": "提示详情", "generateWithPrompt": "生成此提示", "generateWithSettings": "使用这些设置生成", "preset": "预设", "style": "风格", "resolution": "决议", "addImage": "添加图片", "modelPreset": "模型/预设", "imageDimensions": "图像尺寸", "yourImage": "你的图片", "generate": "生成", "nav.aitool": "AI工具", "nav.history": "历史", "nav.api": "API", "nav.login": "登录", "nav.orders": "订单", "3D Render": "3D渲染", "Acrylic": "丙烯酸", "Anime General": "动漫综述", "Creative": "有创意的", "Dynamic": "动态", "Fashion": "时尚", "Game Concept": "游戏概念", "Graphic Design 3D": "三维图形设计", "Illustration": "插图", "None": "无", "Portrait": "肖像", "Portrait Cinematic": "肖像电影模式", "Portrait Fashion": "肖像时尚", "Ray Traced": "光线追踪", "Stock Photo": "库存照片", "Watercolor": "水彩画", "AI Image Generator": "人工智能图像生成器", "Generate AI images from text prompts with a magical particle transformation effect": "通过文本提示生成具有神奇粒子变换效果的AI图像", "Enter your prompt": "输入您的提示", "Generating...": "生成中...", "Generate Image": "生成图像", "Enter a prompt and click Generate Image to create an AI image": "输入提示并点击生成图像以创建AI图像", "Prompt:": "提示：", "Download": "下载", "How It Works": "工作原理", "This AI image generator uses a particle-based transformation effect to visualize the creation process. When you enter a prompt and click 'Generate', the system:": "这个AI图像生成器使用基于粒子的变换效果来可视化创建过程。当您输入提示并点击'生成'时，系统会：", "Sends your prompt to an AI image generation API": "将您的提示发送到AI图像生成API", "Creates a particle system with thousands of tiny particles": "创建一个包含数千个微小粒子的粒子系统", "Transforms the random noise particles into the generated image": "将随机噪声粒子转换为生成的图像", "The particles start in a random noise pattern and then smoothly transform into the final image, creating a magical effect that simulates the AI's creative process.": "粒子从随机噪声模式开始，然后平滑地转变为最终图像，创造出模拟AI创作过程的神奇效果。", "Transform": "转换", "Transforming...": "转换中...", "Initializing particles...": "初始化粒子...", "Loading image...": "加载图像...", "Creating particle system...": "创建粒子系统...", "Adding event listeners...": "添加事件监听器...", "Ready!": "准备就绪！", "AI Image Particle Effect": "AI图像粒子效果", "A demonstration of the BaseMagicImage component that transforms particles into AI-generated images": "BaseMagicImage组件的演示，将粒子转换为AI生成的图像", "Click anywhere or press ESC to close": "单击任意位置或按ESC关闭", "auth.login": "登录", "auth.loginDescription": "登录您的账户以继续", "auth.email": "电子邮件", "auth.enterEmail": "输入您的电子邮件", "auth.password": "密码", "auth.enterPassword": "输入您的密码", "auth.rememberMe": "记住我", "auth.welcomeBack": "欢迎回来", "auth.dontHaveAccount": "没有账户？", "auth.signUp": "注册", "auth.forgotPassword": "忘记密码？", "auth.signupFailed": "注册失败", "auth.signupFailedDescription": "注册过程中出现错误。请重试。", "auth.bySigningIn": "通过登录，您同意我们的", "auth.termsOfService": "服务条款", "auth.signUpTitle": "注册", "auth.signUpDescription": "创建账户以开始", "auth.name": "姓名", "auth.enterName": "输入您的姓名", "auth.createAccount": "创建账户", "auth.alreadyHaveAccount": "已有账户？", "auth.bySigningUp": "通过注册，您同意我们的", "auth.backToHome": "返回首页", "auth.notVerifyAccount": "您的账户尚未验证。请验证您的账户以继续", "auth.verifyAccount": "验证账户", "auth.resendActivationEmail": "重新发送激活邮件", "auth.accountRecovery": "账户恢复", "auth.accountRecoveryTitle": "恢复您的账户", "auth.accountRecoveryDescription": "输入您的邮箱以接收密码重置说明", "auth.sendRecoveryEmail": "发送恢复邮件", "auth.recoveryEmailSent": "恢复邮件已发送", "auth.recoveryEmailSentDescription": "请检查您的邮箱以获取密码重置说明", "auth.resetPassword": "重置密码", "auth.resetPasswordTitle": "重置您的密码", "auth.resetPasswordDescription": "输入您的新密码", "auth.newPassword": "新密码", "auth.confirmPassword": "确认密码", "auth.enterNewPassword": "输入您的新密码", "auth.enterConfirmPassword": "确认您的新密码", "auth.passwordResetSuccess": "密码重置成功", "auth.passwordResetSuccessDescription": "您的密码已成功重置。现在您可以使用新密码登录", "auth.activateAccount": "激活账户", "auth.activateAccountTitle": "激活您的账户", "auth.activateAccountDescription": "您的账户正在激活中...", "auth.accountActivated": "账户已激活", "auth.accountActivatedDescription": "您的账户已成功激活。现在您可以登录", "auth.activationFailed": "激活失败", "auth.activationFailedDescription": "无法激活您的账户。请重试或联系支持", "auth.backToLogin": "返回登录", "auth.loginFailed": "登录失败", "auth.loginWithGoogle": "使用Google登录", "auth.google": "Google", "auth.filter": "Filter", "validation.invalidEmail": "无效的电子邮件", "validation.passwordMinLength": "密码必须至少8个字符", "validation.nameRequired": "姓名是必需的", "validation.required": "此字段为必填项", "validation.passwordsDoNotMatch": "密码不匹配", "imageSelect.pleaseSelectImageFile": "请选择一个图像文件", "imageSelect.selectedImage": "已选择的图像", "imageSelect.removeImage": "删除图像", "pixelReveal.loading": "加载图像中...", "pixelReveal.processing": "处理图像中...", "pixelReveal.revealComplete": "图像显示完成", "SIGNIN_WRONG_EMAIL_PASSWORD": "邮箱或密码错误", "Try again": "重试", "aiToolMenu.imagen": "Imagen", "aiToolMenu.videoGen": "视频生成", "aiToolMenu.speechGen": "语音生成", "aiToolMenu.musicGen": "音乐生成", "aiToolMenu.imagen3": "Imagen 3", "aiToolMenu.imagen3Description": "生成高质量、详细的图像，具有准确的文本渲染功能，用于创意视觉内容。", "aiToolMenu.imagen4": "Imagen 4", "aiToolMenu.imagen4Description": "前所未有地表达您的想法——使用Imagen，创意无限。", "aiToolMenu.gemini2Flash": "Gemini 2.0 Flash", "aiToolMenu.gemini2FlashDescription": "Gemini 2.0 Flash是从文本提示生成图像的强大工具。", "aiToolMenu.veo2": "Veo 2", "aiToolMenu.veo2Description": "比以往更强的控制力、一致性和创造力。", "aiToolMenu.veo3": "Veo 3", "aiToolMenu.veo3Description": "视频与音频的结合。我们最新的视频生成模型，旨在为电影制作人和故事讲述者提供支持。", "aiToolMenu.gemini25Pro": "Gemini 2.5 Pro", "aiToolMenu.gemini25ProDescription": "最先进的文本转语音模型。", "aiToolMenu.gemini25Flash": "Gemini 2.5 Flash", "aiToolMenu.gemini25FlashDescription": "大规模处理（例如多个PDF）。\n需要思考的低延迟、高容量任务\n代理用例", "aiToolMenu.link": "链接", "aiToolMenu.linkDescription": "使用超强的NuxtLink。", "aiToolMenu.soon": "即将推出", "readArticle": "阅读文章", "switchToLightMode": "切换到明亮模式", "switchToDarkMode": "切换到深色模式", "profile": "个人资料", "buyCredits.checkout": "结账", "buyCredits.checkoutDescription": "确认您的订单，然后选择付款方式。", "buyCredits.orderDetail": "订单详情", "buyCredits.credits": "积分", "buyCredits.pricePerUnit": "单价", "buyCredits.totalCredits": "总积分", "buyCredits.totalPrice": "总价", "buyCredits.payment": "付款", "buyCredits.submit": "提交", "buyCredits.cancel": "取消", "pricing.title": "价格", "pricing.description": "为您的图像生成需求选择完美的计划", "pricing.comingSoon": "即将推出", "pricing.comingSoonDescription": "我们的定价计划正在最终确定中。请稍后回来查看更新。", "magicImageDemo.title": "AI图像粒子效果", "magicImageDemo.description": "演示BaseMagicImage组件将粒子转换为AI生成的图像", "magicImageDemo.image": "图像", "magicImageDemo.aboutTitle": "关于此组件", "magicImageDemo.aboutDescription": "BaseMagicImage组件使用Three.js创建粒子系统，可以在随机位置和AI生成的图像之间转换。粒子以旋涡和流动效果移动，创造神奇的转换。", "magicImageDemo.featuresTitle": "特性", "magicImageDemo.features.particleRendering": "基于粒子的图像渲染", "magicImageDemo.features.smoothTransitions": "随机粒子位置和图像形成之间的平滑过渡", "magicImageDemo.features.interactiveControls": "交互式相机控制（拖动旋转，滚动缩放）", "magicImageDemo.features.customizable": "可自定义粒子数量和动画持续时间", "magicImageDemo.features.automatic": "自动或手动转换触发", "magicImageDemo.howItWorksTitle": "工作原理", "magicImageDemo.howItWorksDescription": "该组件分析图像的像素并创建3D粒子系统，其中每个粒子代表一个像素。较亮的像素更靠近查看者，创造微妙的3D效果。粒子最初在3D空间中随机散布，然后在触发时动画形成图像。", "privacy.title": "隐私政策", "privacy.description": "了解我们如何保护您的隐私和处理您的数据", "privacy.informationWeCollect": "我们收集的信息", "privacy.dataSecurity": "数据安全", "privacy.dataSecurityDescription": "我们实施适当的安全措施来保护您的个人信息免受未经授权的访问、更改、披露或破坏。", "privacy.contactUs": "联系我们", "terms.title": "服务条款", "terms.description": "使用Imagen服务的条款和条件", "terms.acceptanceOfTerms": "1. 接受条款", "terms.acceptanceOfTermsDescription": "通过访问和使用Imagen服务，您接受并同意受本协议的条款和条件约束。", "terms.useOfService": "2. 服务使用", "terms.userAccounts": "3. 用户账户", "terms.userAccountsDescription": "您有责任维护您的账户和密码的机密性。", "terms.intellectualProperty": "4. 知识产权", "terms.intellectualPropertyDescription": "我们服务上可用的所有内容和材料均受知识产权保护。", "terms.termination": "5. 终止", "terms.terminationDescription": "我们可能会完全自行决定终止或暂停您的账户和对服务的访问。", "terms.disclaimers": "6. 免责声明", "terms.disclaimersDescription": "服务按\"现状\"提供，不提供任何形式的保证。", "terms.contactUsTerms": "联系我们", "terms.contactUsTermsDescription": "如果您对这些服务条款有任何疑问，请通过我们的支持渠道联系我们。", "appName": "GeminiGen.AI", "quickTopUp": "快速充值", "customTopUp": "自定义充值", "numberOfCredits": "积分数量", "paypal": "PayPal", "paypalDescription": "使用您的PayPal账户安全支付", "debitCreditCard": "借记卡或信用卡", "cardDescription": "Visa, Mastercard, American Express", "payWithCrypto": "使用加密货币支付", "cryptoDescription": "比特币、以太坊和其他加密货币", "profileMenu.guide": "指南", "profileMenu.logo": "标志", "profileMenu.settings": "设置", "profileMenu.components": "组件", "loadingMoreItems": "正在加载更多项目...", "promptLabel": "提示词：", "Describe the video you want to generate...": "描述您想生成的视频...", "cancel": "取消", "confirm": "确认", "history.tabs.imagen": "图像", "history.tabs.video": "视频", "history.tabs.speech": "演讲", "history.tabs.music": "音乐", "history.tabs.history": "历史", "videoExamples": "视频示例", "videoExamplesDescription": "探索这些视频示例及其提示和设置。点击任意“使用此提示”按钮即可将提示复制到您的输入字段。", "useThisPrompt": "使用此提示", "model": "模型", "duration": "持续时间", "videoTypeSelection": "选择视频类型", "notifications.title": "Notifications", "notifications.description": "Your recent notifications and updates", "notifications.totalCount": "{count} notifications", "notifications.markAllRead": "Mark all as read", "notifications.loadMore": "Load more", "notifications.close": "Close", "notifications.empty.title": "No notifications", "notifications.empty.description": "You're all caught up! No new notifications to show.", "notifications.error.title": "Error loading notifications", "notifications.types.default.title": "Notification", "notifications.types.default.description": "You have a new notification", "notifications.types.video_1.title": "视频生成中", "notifications.types.video_1.description": "视频生成正在等待处理", "notifications.types.video_2.title": "视频生成完成", "notifications.types.video_2.description": "视频已成功生成", "notifications.types.video_3.title": "视频生成失败", "notifications.types.video_3.description": "视频生成失败", "notifications.types.image_1.title": "图像生成中", "notifications.types.image_1.description": "图像生成正在等待处理", "notifications.types.image_2.title": "图像生成完成", "notifications.types.image_2.description": "图像已成功生成", "notifications.types.image_3.title": "图像生成失败", "notifications.types.image_3.description": "图像生成失败", "notifications.types.tts_history_1.title": "音频生成中", "notifications.types.tts_history_1.description": "文本转语音正在等待处理", "notifications.types.tts_history_2.title": "音频生成完成", "notifications.types.tts_history_2.description": "语音合成功能已成功生成音频", "notifications.types.tts_history_3.title": "音频生成失败", "notifications.types.tts_history_3.description": "文本转语音生成失败", "notifications.types.voice_training_1.title": "语音训练待定", "notifications.types.voice_training_1.description": "语音训练正在等待处理", "notifications.types.voice_training_2.title": "语音训练完成", "notifications.types.voice_training_2.description": "自定义语音模型训练已成功完成。", "notifications.types.voice_training_3.title": "语音训练失败", "notifications.types.voice_training_3.description": "语音训练失败", "notifications.types.music_1.title": "音乐生成中", "notifications.types.music_1.description": "音乐生成正在等待处理。", "notifications.types.music_2.title": "音乐生成完成", "notifications.types.music_2.description": "人工智能音乐已成功生成。", "notifications.types.music_3.title": "音乐生成失败", "notifications.types.music_3.description": "音乐生成失败", "notifications.types.speech_1.title": "语音生成待定", "notifications.types.speech_1.description": "您的语音生成请求正在等待处理。", "notifications.types.speech_2.title": "语音生成完成", "notifications.types.speech_2.description": "您的演讲已成功生成。", "notifications.types.speech_3.title": "语音生成失败", "notifications.types.speech_3.description": "您的语音生成失败。请再试一次。", "notifications.time.justNow": "刚才", "notifications.time.minutesAgo": "{minutes}分钟前", "notifications.time.hoursAgo": "{hours}小时前", "notifications.time.yesterday": "昨天", "notifications.status.processing.title": "处理中", "notifications.status.processing.description": "您的请求正在处理中", "notifications.status.success.title": "已完成", "notifications.status.success.description": "成功完成", "notifications.status.failed.title": "失败", "notifications.status.failed.description": "处理过程中发生错误", "notifications.status.warning.title": "警告", "notifications.status.warning.description": "已完成，但有警告", "notifications.status.pending.title": "待处理", "notifications.status.pending.description": "等待处理", "notifications.status.cancelled.title": "取消", "notifications.status.cancelled.description": "请求已取消", "footer.nuxtUIOnDiscord": "Nuxt UI on Discord", "historyPages.imagenDescription": "Browse your AI-generated images and artwork", "historyPages.musicDescription": "Browse your AI-generated music and audio content", "historyPages.speechDescription": "Browse your AI-generated speech and voice content", "historyPages.videoDescription": "Browse your AI-generated videos and animations", "historyPages.imagenBreadcrumb": "Imagen", "historyPages.musicBreadcrumb": "Music", "historyPages.speechBreadcrumb": "Speech", "historyPages.videoBreadcrumb": "Video Generation", "historyPages.endOfImagesHistory": "You've reached the end of the images history", "historyPages.endOfMusicHistory": "You've reached the end of the music history", "historyPages.endOfSpeechHistory": "You've reached the end of the speech history", "historyPages.endOfVideoHistory": "You've reached the end of the video history", "historyPages.noVideosFound": "No videos found", "historyPages.noVideosFoundDescription": "Start generating videos to see them here.", "historyPages.errorLoadingVideo": "Error Loading Video", "historyPages.loadingVideoDetails": "Loading video details...", "historyPages.videoDetails": "Video Details", "historyPages.videoInformation": "Video Information", "historyPages.videoNotFound": "The video you are looking for could not be found or loaded.", "historyPages.backToLibrary": "回到图书馆", "historyPages.aiContentLibraryTitle": "人工智能内容库", "historyPages.aiContentLibraryDescription": "浏览和管理不同类别的AI生成内容", "profileSettings.emailNotifications": "Email Notifications", "profileSettings.marketingEmails": "Marketing Emails", "profileSettings.securityAlerts": "Security Alerts", "settings": "设置", "userMenu.profile": "个人资料", "userMenu.buyCredits": "购买积分", "userMenu.settings": "设置", "userMenu.api": "API", "userMenu.logout": "退出登录", "userMenu.greeting": "你好，{name}", "formats.mp3": "MP3", "formats.wav": "WAV", "channels.mono": "单声道", "channels.stereo": "立体声", "options.allow": "允许", "options.dontAllow": "不允许", "options.voices": "语音", "options.pickVoice": "选择语音", "voiceTypes.systemVoices": "系统语音", "voiceTypes.customVoices": "自定义语音", "voiceTypes.premiumVoices": "高级语音", "voiceTypes.userVoices": "用户语音", "common.home": "家", "Describe the speech you want to generate...": "描述您想要生成的讲话内容...", "listenToSpeech": "收听演讲", "generateSimilar": "生成相似", "voice": "语音", "emotion": "情感", "speed": "速度", "speed_settings": "速度设置", "speed_value": "速度值", "speed_slider": "速度滑块", "apply": "申请", "speech_settings": "语音设置", "current_speed": "当前速度", "reset_defaults": "恢复默认设置", "outputFormat": "输出格式", "outputChannel": "输出通道", "selectVoice": "选择语音", "selectEmotion": "选择情感", "selectFormat": "选择格式", "selectChannel": "选择频道", "noVoicesAvailable": "没有可用的语音", "noEmotionsAvailable": "没有情感可用", "searchVoices": "搜索语音...", "searchEmotions": "搜索情感...", "noVoicesFound": "未找到语音", "noEmotionsFound": "未发现情感", "retry": "重试", "noAudioSample": "暂无音频样本可用", "Speech Generation Complete": "语音生成完成", "Your speech has been generated successfully": "您的演讲已成功生成。", "stripe": "Stripe", "stripeDescription": "通过Stripe安全支付", "orders.title": "订单历史记录", "orders.description": "查看您的交易和支付历史", "orders.orderId": "订单编号", "orders.amount": "金额", "orders.credits": "积分", "orders.quantity": "数量", "orders.platform": "平台", "orders.externalId": "交易 ID", "orders.status.completed": "已完成", "orders.status.success": "成功", "orders.status.paid": "已支付", "orders.status.pending": "待处理", "orders.status.processing": "处理", "orders.status.failed": "失败", "orders.status.cancelled": "已取消", "orders.status.error": "错误", "orders.empty.title": "尚未收到订单", "orders.empty.description": "您还没有下过订单。购买积分来开始使用我们的服务。", "orders.empty.action": "购买积分", "orders.endOfList": "您已查看所有订单", "orders.errors.fetchFailed": "加载订单历史失败。请重试。", "orders.meta.title": "订单历史 - Imagen AI", "orders.meta.description": "在Imagen AI上查看您的交易和付款历史", "demo.notifications.title": "通知类型与状态演示", "demo.notifications.description": "不同通知类型与各种状态的示例", "demo.notifications.statusLegend": "状态图例", "demo.speechVoiceSelect.title": "语音选择演示", "demo.speechVoiceSelect.description": "演示带有 modelValue 属性的可重用 BaseSpeechVoiceSelectModal 组件", "aspectRatio": "纵横比", "Image Reference": "图片参考", "personGeneration.dontAllow": "Don't Allow", "personGeneration.allowAdult": "Allow Adult", "personGeneration.allowAll": "Allow All", "safety_filter_level": "安全过滤级别", "used_credit": "已用信用额度", "Safety Filter": "安全过滤器", "safetyFilter.blockLowAndAbove": "下方阻挡和上方阻挡", "safetyFilter.blockMediumAndAbove": "中阻及以上", "safetyFilter.blockOnlyHigh": "仅阻止高位", "safetyFilter.blockNone": "无阻挡", "historyFilter.all": "所有", "historyFilter.imagen": "影像", "historyFilter.videoGen": "视频生成", "historyFilter.speechGen": "语音生成", "Person Generation": "人物生成", "downloadImage": "下载图片", "noImageAvailable": "无可用图片", "enhancePrompt": "增强提示", "addImages": "添加图片", "generateVideo": "生成视频", "happy": "快乐", "sad": "悲伤", "angry": "愤怒", "excited": "兴奋", "laughing": "笑", "crying": "哭泣", "calm": "冷静", "serious": "严肃", "frustrated": "沮丧", "hopeful": "充满希望的", "narrative": "叙事", "kids' storytelling": "儿童讲故事", "audiobook": "有声书", "poetic": "诗意的", "mysterious": "神秘的", "inspirational": "鼓舞人心", "surprised": "惊讶", "confident": "自信", "romantic": "浪漫", "scared": "害怕", "trailer voice": "预告片解说声音", "advertising": "广告", "documentary": "纪录片", "newsreader": "新闻播音员", "weather report": "天气预报", "game commentary": "游戏解说", "interactive": "互动", "customer support": "客户支持", "playful": "俏皮", "tired": "疲倦", "sarcastic": "讽刺的", "disgusted": "厌恶", "whispering": "耳语", "persuasive": "有说服力的", "nostalgic": "怀旧", "meditative": "冥想的", "announcement": "公告", "professional pitch": "专业推介", "casual": "休闲", "exciting trailer": "精彩预告片", "dramatic": "戏剧性", "corporate": "公司", "tech enthusiast": "科技爱好者", "youthful": "年轻", "calming reassurance": "镇静的安慰", "heroic": "英雄的", "festive": "节日的", "urgent": "紧急", "motivational": "励志", "friendly": "友好", "energetic": "精力充沛", "serene": "宁静", "bold": "大胆", "charming": "迷人", "monotone": "单调", "questioning": "质疑", "directive": "指令", "dreamy": "梦幻的", "epic": "史诗", "lyrical": "抒情的", "mystical": "神秘的", "melancholy": "忧郁", "cheerful": "快乐", "eerie": "怪异", "flirtatious": "挑逗的", "thoughtful": "深思熟虑", "cinematic": "电影般的", "humorous": "幽默的", "instructional": "教学", "conversational": "会话型", "apologetic": "抱歉的", "excuse-making": "找借口", "encouraging": "鼓励", "neutral": "中立", "authoritative": "权威的", "sarcastic cheerful": "讽刺而愉快", "reassuring": "令人安心", "formal": "正式的", "anguished": "痛苦的", "giggling": "咯咯笑", "exaggerated": "夸张的", "cold": "冷", "hot-tempered": "急躁", "grateful": "感激", "regretful": "遗憾", "provocative": "挑衅的", "triumphant": "凯旋", "vengeful": "复仇的", "heroic narration": "英雄叙事", "villainous": "邪恶的", "hypnotic": "催眠术", "desperate": "绝望", "lamenting": "悲叹", "celebratory": "庆祝的", "teasing": "戏弄", "exhausted": "筋疲力尽", "questioning suspicious": "质疑可疑事项", "optimistic": "乐观的", "bright, gentle voice, expressing excitement.": "明亮而温柔的声音，表达出兴奋之情。", "low, slow voice, conveying deep emotions.": "低沉缓慢的声音，传递着深刻的情感。", "sharp, exaggerated voice, expressing frustration.": "尖锐夸张的声音，表达了沮丧。", "fast, lively voice, full of enthusiasm.": "快速而充满活力的声音，充满热情。", "interrupted, joyful voice, interspersed with laughter.": "打断了的愉悦声音，夹杂着笑声。", "shaky, low voice, expressing pain.": "颤抖而低沉的声音，表达痛苦。", "gentle, steady voice, providing reassurance.": "温柔而稳定的声音，给予安慰。", "mature, clear voice, suitable for formal content.": "成熟、清晰的声音，适合正式内容。", "weary, slightly irritated voice.": "疲惫而略显恼怒的声音。", "bright voice, conveying positivity and hope.": "明亮的声音，传达积极和希望。", "natural, gentle voice with a slow rhythm.": "自然温和的声音，节奏缓慢。", "lively, engaging voice, captivating for children.": "活泼、有趣的声音，吸引孩子们。", "even, slow voice, emphasizing content meaning.": "平稳缓慢的声音，强调内容的意义。", "rhythmic, emotional voice, conveying subtlety.": "节奏感强、情感丰富的声音，传达微妙之处。", "low, slow voice, evoking curiosity.": "低沉缓慢的声音，引发好奇。", "strong, passionate voice, driving action.": "强烈而充满激情的声音，推动行动。", "high, interrupted voice, expressing astonishment.": "高亢而断续的声音，表达惊讶。", "firm, powerful voice, persuasive and assuring.": "坚定有力的声音，说服力强且令人安心。", "sweet, gentle voice, suitable for emotional content.": "甜美温柔的声音，适合情感内容。", "shaky, interrupted voice, conveying anxiety.": "颤抖而断断续续的声音，传达出焦虑。", "deep, strong voice with emphasis, creating suspense.": "深沉有力的声音，带有强调，营造悬念。", "engaging, lively voice, emphasizing product benefits.": "吸引人且生动的语调，强调产品的优点。", "formal, clear voice with focus on key points.": "正式、清晰的语调，重点突出。", "calm, profound voice, delivering authenticity.": "冷静深沉的声音，传递真实感。", "standard, neutral voice, clear and precise.": "标准、中性、清晰准确的声音。", "bright, neutral voice, suitable for concise updates.": "明亮、中性的声音，适合简洁的更新。", "fast, lively voice, stimulating excitement.": "快速而充满活力的声音，激发兴奋。", "friendly, approachable voice, encouraging engagement.": "友好、平易近人的声音，鼓励互动。", "empathetic, gentle voice, easy to connect with.": "富有同情心、温柔的声音，容易与人产生共鸣。", "clear voice, emphasizing questions and answers.": "清晰的声音，强调问题和答案。", "cheerful, playful voice with a hint of mischief.": "欢快、顽皮的声音中带有一丝顽皮。", "slow, soft voice lacking energy.": "缓慢而柔和的声音缺乏能量。", "ironic, sharp voice, sometimes humorous.": "讽刺而尖锐的声音，有时又带有幽默感。", "cold voice, clearly expressing discomfort.": "冷冷的声音，清晰地表达出不适。", "soft, mysterious voice, creating intimacy.": "柔和而神秘的声音，营造出亲密感。", "emotional voice, convincing the listener to act.": "情感声音，促使听者采取行动。", "gentle voice, evoking feelings of reminiscence.": "温柔的声音，唤起回忆的感觉。", "even, relaxing voice, suitable for mindfulness.": "声音平稳放松，适合正念。", "clear voice, emphasizing key words.": "清晰的语音，强调关键词。", "confident, clear voice, ideal for business presentations.": "自信、清晰的声音，适合商务演示。", "natural, friendly voice, as if talking to a friend.": "自然、友好的声音，就像在和朋友聊天。", "fast, powerful voice, creating tension and excitement.": "快速而有力的声音，营造紧张和兴奋的氛围。", "emphasized, suspenseful voice, creating intensity.": "强调的、悬疑的声音，营造出强烈的紧张感。", "professional, formal voice, suitable for business content.": "专业、正式的语气，适合商业内容。", "energetic, lively voice, introducing new technologies.": "充满活力的声音，介绍新技术。", "vibrant, cheerful voice, appealing to younger audiences.": "充满活力、欢快的声音，吸引年轻观众。", "gentle, empathetic voice, easing concerns.": "温柔而富有同理心的声音，缓解担忧。", "strong, decisive voice, full of inspiration.": "有力而果断的声音，充满了启发。", "bright, excited voice, suitable for celebrations.": "明亮而兴奋的声音，适合庆祝活动。", "fast, strong voice, emphasizing urgency.": "快速、有力的声音，强调紧迫性。", "passionate, inspiring voice, encouraging action.": "充满激情，鼓舞人心的声音，激励行动。", "warm, approachable voice, fostering connection.": "温暖、平易近人的声音，促进联系。", "fast, powerful voice, brimming with enthusiasm.": "快速而强有力的声音，充满热情。", "slow, gentle voice, evoking peace and tranquility.": "缓慢而温柔的声音，唤起和平与宁静。", "firm, assertive voice, exuding confidence.": "坚定自信的声音，散发自信。", "warm, captivating voice, leaving a strong impression.": "温暖而迷人的声音，留下深刻的印象。", "flat, unvaried voice, conveying neutrality or irony.": "平淡无变的声音，传达中立或讽刺。", "curious voice, emphasizing questions.": "好奇的声音，强调问题。", "firm, clear voice, guiding the listener step-by-step.": "坚定而清晰的声音，引导听众一步一步地。", "gentle, slow voice, evoking a floating sensation.": "温柔缓慢的声音，唤起漂浮的感觉。", "deep, resonant voice, emphasizing grandeur.": "低沉而洪亮的声音，强调宏伟。", "soft, melodic voice, similar to singing.": "柔和的旋律声，类似于唱歌。", "low, drawn-out voice, evoking mystery.": "低沉而拖长的声音，引发神秘感。", "slow, low voice, conveying deep sadness.": "低沉的缓慢声音，传达出深深的悲伤。", "bright, energetic voice, full of positivity.": "明亮而充满活力的声音，充满积极性。", "low, whispery voice, evoking fear or strangeness.": "低沉而细语的声音，引起恐惧或怪异感。", "sweet, teasing voice, full of allure.": "甜美而充满诱惑的声音。", "slow, reflective voice, full of contemplation.": "缓慢而富有思考的声音，充满深思。", "resonant, emphasized voice, creating a movie-like effect.": "共鸣的、突出的声音，营造出电影般的效果。", "lighthearted, cheerful voice, sometimes exaggerated.": "轻松愉快，充满活力的声音，有时略显夸张。", "clear, slow voice, guiding the listener step-by-step.": "清晰缓慢的声音，引导听众一步一步地进行。", "natural voice, as if chatting with the listener.": "自然的语音，就像在与听者聊天一样。", "soft, sincere voice, expressing regret.": "轻柔真诚的声音，表达歉意。", "hesitant, uncertain voice, sometimes awkward.": "犹豫不决，不确定的声音，有时显得尴尬。", "warm voice, providing motivation and support.": "温暖的声音，提供动力和支持。", "even voice, free of emotional bias.": "即使是声音，也没有情绪偏见。", "strong, powerful voice, exuding credibility.": "强大有力的声音，充满可信度。", "cheerful voice with an undertone of mockery.": "愉快的声音中含有一丝嘲讽。", "gentle, empathetic voice, providing comfort.": "温柔且富有同情心的声音，带来安慰。", "clear, polite voice, suited for formal occasions.": "清晰礼貌的声音，适合正式场合。", "urgent, shaky voice, expressing distress.": "紧急，颤抖的声音，表露出痛苦。", "interrupted voice, mixed with light laughter.": "断断续续的声音，夹杂着轻轻的笑声。", "loud, emphasized voice, often humorous.": "响亮、强调的声音，常带有幽默感。", "flat, unemotional voice, conveying detachment.": "平淡而无感情的声音，传达出一种冷漠。", "fast, sharp voice, sometimes out of control.": "快速尖锐的声音，有时难以控制。", "warm, sincere voice, expressing appreciation.": "温暖而真诚的声音，表达谢意。", "low, subdued voice, full of remorse.": "低沉压抑的声音，充满悔恨。", "challenging, strong voice, full of insinuation.": "充满挑战，声音洪亮，暗示意味十足。", "loud, powerful voice, full of victory.": "响亮而有力的声音，充满胜利。", "low, cold voice, expressing determination for revenge.": "低沉而冰冷的声音，表达了复仇的决心。", "strong, inspiring voice, emphasizing heroic deeds.": "强烈而鼓舞人心的声音，强调英雄事迹。", "low, drawn-out voice, full of scheming.": "低沉而拖长的声音，充满了算计。", "even, repetitive voice, drawing the listener in.": "即使是重复的声音，也能吸引听众的注意力。", "urgent, shaky voice, expressing hopelessness.": "紧急、颤抖的声音，表达绝望。", "low, sorrowful voice, as if mourning.": "低沉而忧伤的声音，仿佛在哀悼。", "excited, joyful voice, full of festive spirit.": "激动、欢乐的声音，充满节日的气息。", "light, playful voice, sometimes mockingly.": "轻盈而戏谑的声音，有时带有嘲讽。", "weak, broken voice, expressing extreme fatigue.": "虚弱、沙哑的声音，表达出极度的疲惫。", "slow, emphasized voice, full of suspicion.": "缓慢而强调的语气，充满怀疑。", "bright, hopeful voice, creating positivity.": "明亮而充满希望的声音，带来积极向上的力量。", "Your audio will be processed with the latest stable model.": "您的音频将通过最新的稳定模型进行处理。", "Your audio will be processed with the latest beta model.": "您的音频将使用最新的测试模型进行处理。", "You don't have any saved prompts yet.": "您还没有保存任何提示。", "Commercial Use": "商业用途", "Other people’s privacy": "他人的隐私", "You must respect the privacy of others when using our services. Do not upload or create speech output containing personal information, confidential data, or copyrighted material without permission.": "在使用我们的服务时，您必须尊重他人的隐私。未经许可，请勿上传或创建包含个人信息、机密数据或受版权保护的材料的语音输出。", "{price}$ per credit": "每学分{price}美元", "Pricing": "定价", "Simple and flexible. Only pay for what you use.": "简单灵活。只为所用付费。", "Pay as you go": "随用随付", "Flexible": "灵活", "Input characters": "输入字符", "Audio model": "音频模型", "Credits": "鸣谢", "Cost": "成本", "HD quality voices": "高清音质语音", "Advanced model": "高级模型", "Buy now": "立即购买", "Paste your text to calculate": "粘贴您的文本进行计算", "Paste your text here...": "将您的文本粘贴到此处...", "Calculate": "计算", "Estimate your cost by drag the slider below or": "通过拖动下面的滑块来估算您的费用或", "calming": "平静", "customer": "客户", "exciting": "令人兴奋", "excuse": "借口", "game": "游戏", "hot": "热", "kids": "孩子们", "professional": "专业", "tech": "科技", "trailer": "预告片", "weather": "天气", "No thumbnail available": "没有可用的缩略图", "Debit or Credit Card": "借记卡或信用卡", "Visa, Mastercard, American Express and more": "维萨卡、万事达卡、美国运通等", "Top up now": "立即充值", "noVideoAvailable": "没有可用视频", "common.back": "返回", "common.edit": "编辑", "common.save": "保存", "common.cancel": "取消", "common.delete": "删除", "common.copy": "复制", "common.copied": "复制", "common.manage": "管理", "aiToolMenu.textToImage": "文本转图像", "profileMenu.integration": "集成", "videoTypes.examples.tikTokDanceTrend": "抖音舞蹈潮流", "videoTypes.examples.energeticDanceDescription": "充满活力的舞蹈视频，色彩鲜艳，快速剪辑，流行音乐，竖屏格式，社交媒体风格。", "videoTypes.examples.instagramReel": "Instagram视频", "videoTypes.examples.lifestyleDescription": "具有美感的生活方式内容，流畅的过渡，时尚的效果，引人入胜的故事讲述", "videoTypes.examples.comedySketch": "喜剧小品", "videoTypes.examples.funnyComedyDescription": "搞笑的喜剧场景中有富有表现力的角色、幽默的情节、有趣的对话、轻松愉快的氛围。", "videoTypes.examples.productLaunchAd": "产品发布广告", "videoTypes.examples.professionalCorporateDescription": "专业企业视频，包含高管演示、整洁的办公室环境、商务正装风格", "videoTypes.examples.quickPromoVideo": "快速宣传视频", "videoTypes.examples.fastPacedPromoDescription": "快速促销内容与高效制作、经济实惠的视觉效果、精简的信息传递", "videoTypes.examples.birthdayGreeting": "生日祝福", "videoTypes.examples.personalizedBirthdayDescription": "个性化生日视频配有节日装饰、温馨灯光、庆祝氛围和真诚的祝福。", "videoTypes.examples.brandStoryVideo": "品牌故事视频", "videoTypes.examples.tutorialVideo": "教学视频", "videoTypes.examples.manOnThePhone": "正在打电话的人", "videoTypes.examples.runningSnowLeopard": "奔跑的雪豹", "videoTypes.examples.snowLeopard": "雪豹", "videoTypes.styles.cartoonAnimated": "卡通或动画风格视频", "videoTypes.styles.naturalDocumentary": "自然纪录片风格的画面", "videoTypes.styles.naturalLifelike": "自然逼真的视频风格", "videoTypes.styles.professionalMovieQuality": "专业电影般的质量与戏剧化的灯光效果", "videoTypes.styles.creativeStylized": "创意和风格化的视频效果", "videoTypes.styles.retroVintage": "复古或怀旧视频美学", "historyFilter.dialogueGen": "对话生成", "historyFilter.speechGenDocument": "从文档生成语音", "demo.notifications.availableNotificationTypes": "可用通知类型", "demo.speechVoiceSelect.example1": "示例 1：默认用法", "demo.speechVoiceSelect.example2": "示例2：小尺寸", "demo.speechVoiceSelect.example3": "示例3：大尺寸", "demo.speechVoiceSelect.example4": "示例4：多个错误示例", "demo.speechVoiceSelect.example5": "示例5：状态比较", "demo.speechVoiceSelect.mainNarrator": "主讲人", "demo.speechVoiceSelect.characterVoice": "角色配音", "demo.speechVoiceSelect.selectedVoicesSummary": "精选声音摘要：", "demo.speechVoiceSelect.clearAll": "全部清除", "demo.speechVoiceSelect.setRandomVoices": "设置随机语音", "demo.speechVoiceSelect.logToConsole": "记录到控制台", "demo.speechVoiceSelect.notSelected": "未选中", "demo.speechVoiceSelect.voiceSelectionsChanged": "语音选择已更改", "demo.historyWrapper.title": "历史封装器状态徽章演示", "demo.historyWrapper.normalStatus": "示例 1：正常状态（状态 = 1）", "demo.historyWrapper.processingStatus": "示例 2：处理中状态（状态 = 2）", "demo.historyWrapper.errorStatus": "示例 3：错误状态（状态 = 3）- 显示错误徽章", "demo.historyWrapper.multipleErrorExamples": "示例 4：多个错误示例", "demo.historyWrapper.statusComparison": "示例 5：状态比较", "demo.historyWrapper.normalImageGeneration": "正常图像生成", "demo.historyWrapper.videoGenerationInProgress": "视频生成中", "demo.historyWrapper.speechGenerationFailed": "语音生成失败", "demo.historyWrapper.imageFailed": "图像失败", "demo.historyWrapper.videoFailed": "视频失败", "demo.historyWrapper.speechFailed": "语音失败", "demo.historyWrapper.statusSuccess": "状态：成功", "demo.historyWrapper.statusProcessing": "状态：处理中", "demo.historyWrapper.statusError": "状态：错误", "demo.historyWrapper.status1Success": "状态 1：成功", "demo.historyWrapper.status2Processing": "状态2：处理中", "demo.historyWrapper.badgeBehavior": "徽章行为:", "demo.historyWrapper.showsOnlyTypeAndStyle": "仅显示类型和样式徽章", "demo.historyWrapper.showsTypeStyleAndError": "显示类型、样式和带有警报图标的红色错误徽章", "demo.historyWrapper.redBackgroundWithWhite": "红色背景，白色文字和警告圆圈图标", "demo.historyWrapper.allBadgesHideOnHover": "所有徽章在悬停时隐藏以显示覆盖内容", "demo.speechVoiceCaching.title": "语音缓存测试", "demo.speechVoiceCaching.description": "测试以检查不同组件之间的缓存语音功能", "demo.speechVoiceCaching.component1Modal": "组件 1 - 模态框", "demo.speechVoiceCaching.component3RegularSelect": "组件3 - 常规选择", "demo.speechVoiceCaching.forceReloadVoices": "强制重新加载语音", "demo.speechVoiceCaching.clearAllSelections": "清除所有选择", "demo.speechVoiceCaching.logStoreState": "日志存储状态", "demo.speechVoiceCaching.refreshPageInstructions": "刷新页面并打开任何组件 - 将重新加载", "demo.speechVoiceCaching.checkNetworkTab": "检查网络选项卡以确认API调用", "demo.speechVoiceCaching.selectedVoicePersist": "选定的语音将通过localStorage持久化。", "demo.speechVoiceCaching.pageMounted": "页面挂载，如果需要，商店将自动初始化。", "integration.title": "集成", "integration.subtitle": "管理您的API密钥和集成设置", "integration.apiKeys": "API密钥", "integration.apiKeysDescription": "管理您的API密钥以进行编程访问", "integration.webhook": "网络钩子", "integration.webhookDescription": "配置通知的Webhook URL", "apiKeys.title": "API密钥", "apiKeys.subtitle": "管理您的API密钥以进行编程访问", "apiKeys.create": "创建 API 密钥", "apiKeys.createNew": "创建新API密钥", "apiKeys.createFirst": "创建第一个API密钥", "apiKeys.name": "名称", "apiKeys.nameDescription": "为您的API密钥起一个描述性的名称", "apiKeys.namePlaceholder": "例如，我的应用程序API密钥", "apiKeys.nameRequired": "需要提供API密钥名称。", "apiKeys.createdAt": "创建", "apiKeys.noKeys": "没有 API 密钥", "apiKeys.noKeysDescription": "创建您的第一个 API 密钥以开始编程访问", "apiKeys.created": "API密钥创建成功", "apiKeys.createError": "无法创建API密钥", "apiKeys.deleted": "API密钥已成功删除", "apiKeys.deleteError": "删除 API 密钥失败", "apiKeys.deleteConfirm": "删除API密钥", "apiKeys.deleteWarning": "您确定要删除此 API 密钥吗？此操作无法撤销。", "apiKeys.copied": "API密钥已复制到剪贴板", "apiKeys.copyError": "无法复制API密钥", "webhook.title": "Webhook配置", "webhook.subtitle": "配置网络钩子URL以实现实时通知", "webhook.configuration": "Webhook网址", "webhook.currentUrl": "当前 Webhook URL", "webhook.currentUrlDescription": "此 URL 将接收用于 webhook 事件的 POST 请求。", "webhook.notConfigured": "未配置Webhook URL", "webhook.url": "Webhook URL", "webhook.urlDescription": "输入您希望接收 webhook 通知的 URL。", "webhook.urlPlaceholder": "https://your-domain.com/webhook", "webhook.urlRequired": "请先输入一个 Webhook URL", "webhook.invalidUrl": "请输入有效的URL", "webhook.saved": "Webhook URL保存成功", "webhook.saveError": "无法保存 webhook URL", "webhook.test": "测试", "webhook.testSent": "测试已发送", "webhook.testDescription": "测试 webhook 发送成功。", "webhook.information": "Web钩信息", "webhook.howItWorks": "如何运作", "webhook.description": "配置完成后，每当您的账户中发生某些事件时，我们将向您的 webhook URL 发送 HTTP POST 请求。", "webhook.events": "网络钩子事件", "webhook.imageGenerated": "图像生成完成", "webhook.imageGenerationFailed": "图像生成失败", "webhook.creditUpdated": "信用余额已更新", "webhook.payloadFormat": "有效载荷格式", "webhook.payloadDescription": "Webhook请求将以JSON格式发送，其结构如下：", "webhook.security": "安全", "webhook.securityDescription": "我们建议使用HTTPS网址并实施签名验证以确保Webhook的真实性。", "error.general": "错误", "error.validation": "验证错误", "error.required": "必填字段", "success.saved": "保存成功", "success.created": "创建成功", "success.deleted": "删除成功", "success.copied": "已复制到剪贴板", "confirmDelete": "确认删除", "confirmDeleteDescription": "您确定要删除此项目吗？此操作无法撤销。", "historyDeleted": "历史项目删除成功", "deleteError": "删除历史记录项失败", "Regenerate Image": "重新生成图像", "You haven't made any changes to the settings. Are you sure you want to regenerate the same image?": "您尚未对设置进行任何更改。您确定要重新生成相同的图像吗？", "Yes, Regenerate": "是的，再生", "Cancel": "取消", "models.imagen4Fast": "快速图像4", "models.imagen4Ultra": "Ultra 4 影像", "voiceTypes.favoriteVoices": "最喜爱的声音", "voiceTypes.geminiVoices": "双子座之声", "speech.dialogueGeneration.complete": "对话生成完成", "speech.dialogueGeneration.failed": "对话生成失败", "speech.dialogueGeneration.pending": "对话生成待定", "speech.dialogueGeneration.dialogueGen": "对话生成", "speech.dialogueGeneration.successMessage": "您的对话已成功生成", "speech.speechGeneration.complete": "语音生成完成", "speech.speechGeneration.failed": "语音生成失败", "speech.speechGeneration.pending": "语音生成待定", "speech.speechGeneration.successMessage": "您的演讲稿已成功生成。", "speech.speechGeneration.requestWaiting": "您的语音生成请求正在等待处理。", "speech.errors.failedToLoadEmotions": "无法加载情感", "tts-document": "文件转语音", "assignVoicesToSpeakers": "为说话者分配声音", "speakers": "扬声器", "addSpeaker": "添加扬声器", "noVoiceAssigned": "没有分配语音", "noSpeakersAdded": "尚未添加演讲者。", "assignVoiceToSpeaker": "将语音分配给{speaker}", "assigned": "分配", "assign": "分配", "editSpeaker": "编辑发言者", "speakerName": "演讲者姓名", "enterSpeakerName": "输入发言者姓名", "save": "保存", "speaker": "扬声器", "assignVoices": "分配语音", "speakersWithVoices": "{assigned}/{total}个演讲者有声音", "dialogs": "对话", "addDialog": "添加对话框", "enterDialogText": "输入对话文本...", "selectSpeaker": "选择扬声器", "generateDialogSpeech": "生成对话语音", "voice 1": "声音1", "voice 2": "声音2", "uuid": "UUID", "output_format": "输出格式", "output_channel": "输出通道", "file_name": "文件名", "file_size": "文件大小", "speakers_count": "扬声器数量", "custom_prompt": "自定义提示", "Please wait a moment...": "请稍等一下...", "Click to copy": "点击复制", "Copied to clipboard": "已复制到剪贴板", "UUID has been copied to clipboard": "UUID已复制到剪贴板中", "Credits: {credits} remaining": "{credits}积分剩余", "This generation will cost: {cost} Credits": "这一代将花费：{cost}积分", "This generation will cost: {cost} Credits for {duration}s": "这次生成将花费：{cost}积分（{duration}秒）", "Your generated video will appear here": "此处将显示您生成的视频。", "Regenerate Video": "重新生成视频", "You haven't made any changes to the settings. Are you sure you want to regenerate the same video?": "您尚未对设置进行任何更改。您确定要重新生成相同的视频吗？", "Your generated speech will appear here": "您的生成语音将显示在此处。", "Regenerate Speech": "重新生成语音", "You haven't made any changes to the settings. Are you sure you want to regenerate the same speech?": "您尚未对设置进行任何更改。您确定要重新生成相同的语音吗？", "Generated Speech": "生成的语音", "Generating speech...": "生成语音...", "View Details": "查看详情", "Speech Examples": "演讲示例", "Click on any example to use its prompt for speech generation": "单击任意示例以使用其提示进行语音生成。", "Click to use": "点击使用", "videoStyles.selectVideoStyle": "选择视频风格", "videoStyles.cinematic": "电影般的", "videoStyles.realistic": "现实的", "videoStyles.animated": "动画的", "videoStyles.artistic": "艺术的", "videoStyles.documentary": "纪录片", "videoStyles.vintage": "复古", "ui.buttons.downloadApp": "下载应用", "ui.buttons.signUp": "注册", "ui.buttons.viewDetails": "查看详情", "ui.buttons.seeLater": "再见", "ui.buttons.selectFile": "选择文件", "ui.buttons.selectFiles": "选择文件", "ui.buttons.pickAVoice": "选择一个声音", "ui.buttons.topUpNow": "立即充值", "ui.buttons.pressEscToClose": "按ESC关闭", "ui.labels.clickToCopy": "点击复制", "ui.labels.copiedToClipboard": "已复制到剪贴板", "ui.labels.noAudioAvailable": "无音频可用", "ui.labels.noThumbnailAvailable": "无缩略图可用", "ui.labels.noPromptAvailable": "无提示可用", "ui.labels.videoModel": "视频模型", "ui.labels.speechModel": "语音模型", "ui.labels.generatedSpeech": "生成的语音", "ui.labels.defaultVoice": "默认语音", "ui.labels.selectAnyVoice": "选择任一语音", "ui.labels.cameraMotion": "相机运动", "ui.labels.transform": "变形金刚", "ui.labels.transforming": "正在转换...", "ui.messages.imageLoaded": "图像已加载", "ui.messages.imageRevealComplete": "图像显示完成", "ui.messages.processingImage": "处理图像", "ui.messages.videoLoaded": "视频已加载", "ui.messages.videoProcessing": "视频处理", "ui.messages.invalidDownloadLink": "下载链接无效", "ui.messages.pleaseSelectSupportedFile": "请选择受支持的文件", "ui.messages.deleteConfirm": "确认删除", "ui.messages.deleteFailed": "删除失败", "ui.messages.youHaveNewNotification": "您有一条新通知", "ui.messages.yourGenerationIsReady": "你的这一代已经准备好了", "ui.errors.errorLoadingImage": "加载图像时出错：", "ui.errors.failedToCopy": "复制失败：", "ui.errors.failedToPlayAudioPreview": "无法播放音频预览：", "ui.errors.wavesurferError": "Wavesurfer错误：", "ui.errors.somethingWentWrong": "出了点问题。请再试一次。", "ui.errors.supabaseUrlRequired": "需要 Supabase URL 和匿名密钥", "dialog.startTypingHere": "在此处开始输入对话...", "payment.debitCreditCard": "借记卡或信用卡", "payment.cardDescription": "维萨、万事达、美国运通等", "Style Description": "样式描述", "Dialog Content": "对话内容", "Your generated dialog will appear here": "您的生成对话将在这里显示。", "Regenerate Dialog": "重新生成对话", "Generated Dialog": "生成对话", "Generating dialog...": "生成对话中...", "Dialog Information": "对话信息", "Audio Player": "音频播放器", "Voices": "声音", "Voice 1": "语音1", "Voice 2": "声音2", "Dialog Examples": "对话示例", "Click on any example to use its style or dialog content": "单击任何示例以使用其样式或对话内容。", "Use Style": "使用样式", "Use Dialog": "使用对话框", "personGeneration": "人物生成", "Imagen": "图像", "On": "上", "Off": "关闭", "Prompts will always be refined to improve output quality": "提示将始终被优化以提高输出质量。", "Prompts will not be modified": "提示不会被修改。", "Tips": "提示", "Your video is still being generated in the background. You can close this page and check the history tab for the generated video and we will notify you when it is ready.": "您的视频仍在后台生成。您可以关闭此页面并在历史记录选项卡中查看生成的视频，我们会在其准备好时通知您。", "Go to History": "去历史", "footer.youtube": "优酷", "footer.doctransGPT": "DoctransGPT", "footer.textToSpeechOpenAI": "文本转语音OpenAI", "footer.privacyPolicy": "隐私政策", "footer.termsOfService": "服务条款", "footer.terms": "术语", "footer.privacy": "隐私", "Generate": "生成", "Prompt": "提示", "Generate Video": "生成视频", "ui.errors.generationFailed": "生成失败", "downloadVideo": "下载视频", "imageStyles.selectImageStyle": "选择图像样式", "imageStyles.none.description": "没有应用特定的风格", "imageStyles.3d-render.description": "渲染三维图像", "imageStyles.acrylic.description": "用丙烯画风格创作图像", "imageStyles.anime-general.description": "生成动漫风格的图像", "imageStyles.creative.description": "应用创意艺术效果", "imageStyles.dynamic.description": "创作充满活力和动感的视觉效果", "imageStyles.fashion.description": "时尚摄影风格图像", "imageStyles.game-concept.description": "为游戏概念艺术设计图像", "imageStyles.graphic-design-3d.description": "应用3D图形设计元素", "imageStyles.illustration.description": "创建插画风格的艺术作品", "imageStyles.portrait.description": "优化人像摄影", "imageStyles.portrait-cinematic.description": "创建电影肖像风格", "imageStyles.portrait-fashion.description": "应用时尚肖像造型", "imageStyles.ray-traced.description": "使用光线追踪效果进行渲染", "imageStyles.stock-photo.description": "创建专业股票照片风格", "imageStyles.watercolor.description": "应用水彩画效果", "imageStyles.examples": "例子", "ui.messages.dragDropOrClick": "将文件拖放到此处或点击选择", "ui.messages.dropFilesHere": "在此处拖放文件", "ui.messages.selectMultipleFiles": "您可以选择多个文件", "ui.messages.selectSingleFile": "选择要上传的文件", "ui.messages.supportedFormats": "支持的格式", "ui.messages.releaseToUpload": "释放以上传", "ui.labels.generatedAudio": "生成的音频", "ui.actions.showResult": "显示结果", "ui.actions.hideResult": "隐藏结果", "ui.messages.speechGenerating": "正在生成语音...", "Your speech is still being generated in the background. You can close this page and check the history tab for the generated audio and we will notify you when it is ready.": "您的语音仍在后台生成中。您可以关闭此页面并在历史记录标签中查看生成的音频，我们会在准备好时通知您。", "downloadAudio": "下载音频", "All Countries": "所有国家", "All Genders": "所有性别", "Country": "国家", "Gender": "性别", "Reset": "重置", "Search by name or description...": "按名称或描述搜索...", "Male": "男性", "Female": "女性", "American": "美国人", "British": "英国的", "Australian": "澳大利亚人", "Indian": "印度的", "Chinese": "中文", "Spanish": "西班牙语", "Canadian": "加拿大人", "Irish": "爱尔兰语", "Singaporean": "新加坡人", "Russian": "俄语", "German": "德语", "Portuguese": "葡萄牙语", "Hindi": "印地语", "Mexican": "墨西哥的", "Latin American": "拉丁美洲", "Argentine": "阿根廷人", "Peninsular": "半岛", "French": "法语", "Parisian": "巴黎人", "Standard": "标准", "Brazilian": "巴西人", "Turkish": "土耳其语", "Istanbul": "伊斯坦布尔", "Bavarian": "巴伐利亚的", "Polish": "波兰语", "Italian": "意大利语", "South African": "南非人", "Scottish": "苏格兰的", "Welsh": "威尔士语", "New Zealand": "新西兰", "Dutch": "荷兰语", "Belgian": "比利时的", "Swedish": "瑞典语", "Norwegian": "挪威语", "Danish": "丹麦语", "Korean": "韩语", "Korean, Seoul": "韩国，首尔", "Japanese": "日语", "Croatian": "克罗地亚语", "Czech": "捷克", "Moravian": "摩拉维亚人", "Zealandic": "新西兰语", "Indonesian": "印度尼西亚语", "Javanese": "爪哇语", "Romanian": "罗马尼亚语", "Swiss": "瑞士", "Vietnamese": "越南语", "Arabic": "阿拉伯语", "Bulgarian": "保加利亚语", "Finnish": "芬兰语", "Greek": "希腊语", "Hungarian": "匈牙利语", "Filipino": "菲律宾语", "History": "历史", "imagen-flash": "双子2.0闪光", "Detail": "细节", "Delete": "删除", "ui.errors.unknownError": "发生未知错误", "ui.errors.tryAgainLater": "请稍后再试", "More": "更多", "tts-text": "音频", "tts-multi-speaker": "音频", "tts-history": "音频", "tts-history_1": "音频", "tts-history_2": "音频", "tts-history_3": "音频", "voice-training": "语音训练", "voice-training_1": "语音训练", "voice-training_2": "语音训练", "Start writing or paste your text here or select a file to generate speech...": "开始书写或将您的文本粘贴到此处，或选择一个文件以生成语音...", "Selecting a voice...": "选择语音...", "Voices Library": "语音库", "Select a voice for your speaker from the library.": "从库中为您的扬声器选择一个声音。", "Next": "下一个", "Back": "返回", "Done": "完成", "I got it!": "我明白了！", "historyPages.endOfHistory": "您已到达历史的尽头。", "Press ESC to close": "按 ESC 关闭", "Your generated image will appear here": "您的生成图像将显示在这里", "Generate Speech": "生成语音", "Start writing or paste your text here to generate speech...": "开始在此处书写或粘贴您的文本以生成语音...", "Video Gen": "视频生成器", "Generate videos from text prompts and images.": "根据文本提示和图像生成视频。", "Speech Gen": "语音生成", "Convert text and documents to natural speech.": "将文本和文档转换为自然语音。", "Dialogue Gen": "对话生成助手", "Create natural conversations with multiple speakers.": "创建多位讲话者的自然对话。", "Veo 2": "我看2", "Text to Video": "文字转视频", "Image to Video": "图像转视频", "Up to 8 seconds": "最多8秒", "1080p Quality": "1080p画质", "Multiple Styles": "多种风格", "Text to Speech": "文本转语音", "Document to Speech": "文档转语音", "Multi-Speaker Support": "多说话者支持", "50+ Voices": "50多个声音", "Multiple Languages": "多种语言", "Emotion Control": "情绪控制", "Multi-Speaker Dialogue": "多说话者对话", "Natural Conversations": "自然对话", "Voice Customization": "语音定制", "Emotion Expression": "情感表达", "Script Generation": "脚本生成", "Audio Export": "音频导出", "Home": "家", "Price per 1 character: {cost} Credits": "每个字符的价格：{cost}点数", "veo-2": "Veo 2", "veo-3": "我看到3", "Your speech generation is still being generated in the background. You can close this page and check the history tab for the generated speech and we will notify you when it is ready.": "您的语音生成正在后台进行。您可以关闭此页面，并在历史记录选项卡中查看生成的语音，完成后我们会通知您。", "Create Another": "创建另一个", "estimated_credit": "预估信用额度", "tts-flash": "双子2.5闪存", "Select Another Voice": "选择其他声音", "Custom prompt {count}": "自定义提示{count}", "Custom prompt": "自定义提示", "Prompt name": "提示名称", "This name will help you identify your prompt.": "此名称将帮助您识别您的提示。", "Save as new": "另存为新文件", "Ok, save it!": "好的，保存吧！", "Don't use": "不要使用", "Use": "使用", "You have the right to use the speech output generated by our services for personal, educational, or commercial purposes.": "您有权将我们服务生成的语音输出用于个人、教育或商业目的。", "Your saved prompts": "您保存的提示", "Success": "成功", "Saved prompt successfully": "成功保存提示", "Error": "错误", "Saved prompt failed": "保存的提示失败", "Updated prompt successfully": "成功更新提示", "Updated prompt failed": "更新的提示失败了", "Are you sure you want to delete this prompt?": "您确定要删除此提示吗？", "Deleted prompt successfully": "成功删除提示", "Deleted prompt failed": "删除的提示失败了", "Enter your custom prompt here.": "在此输入您的自定义提示。", "Ex: Funny prompt": "例如：有趣的提示", "Discard": "丢弃", "Update": "更新", "Edit": "编辑", "Custom Prompt": "自定义提示", "Your credits will never expire.": "您的积分永不过期。", "Available credits": "可用积分", "{n}+ Styles": "{n}+ 样式", "Create images from text prompts.": "根据文本提示创建图像。", "/Image": "/图像", "/Video": "/视频", "/1 character": "/1 字符", "Buy credits": "购买积分", "My Account": "我的账户", "Manage your account, credits, and orders.": "管理您的账户、积分和订单。", "Full Name": "全名", "Total Available Credits": "总可用额度", "Locked Credits": "锁定积分", "Save changes": "保存更改", "Your account has been updated.": "您的账户已更新。", "This field is required.": "此字段为必填项。", "User Info": "用户信息", "Email": "电子邮件", "Used to sign in, for email receipts and product updates.": "用于登录、接收电子邮件收据和产品更新。", "Active and valid credits only": "仅限有效信用", "We lock your credits to perform transactions.": "我们锁定您的信用以进行交易。", "Referral Link": "推荐链接", "Share your referral link to earn credits.": "分享您的推荐链接以赚取积分。", "Referral Code": "推荐码", "Your Referral Code": "您的推荐码", "Copy": "复制", "Copied!": "已复制！", "Orders": "订单", "Manage your orders.": "管理您的订单。", "Will appear on receipts, invoices, and other communication.": "将出现在收据、发票和其他沟通中。", "User Information": "用户信息", "Must be at least 8 characters": "必须至少包含8个字符", "Passwords must be different": "密码必须不同", "Passwords must match": "密码必须匹配", "Current password": "当前密码", "New password": "新密码", "Confirm new password": "确认新密码", "Password": "密码", "Confirm your current password before setting a new one.": "在设置新密码之前，请确认您当前的密码。", "Account": "账户", "No longer want to use our service? You can delete your account here. This action is not reversible. All information related to this account will be deleted permanently.": "不再想使用我们的服务了吗？您可以在这里删除您的账户。此操作不可逆转。与此账户相关的所有信息将被永久删除。", "Delete account": "删除账户", "Change Password": "修改密码", "Security": "安全", "Credit Statistics": "信用统计", "enhance_prompt": "提升提示", "Current Plan": "当前计划", "When you buy credits, you will be upgraded to Premium Plan.": "当您购买积分时，您的账户将升级到高级计划。", "Available Credits": "可用额度", "Purchased Credits": "购买积分", "Plan Credits": "计划额度", "profile.passwordChanged": "密码已更改", "profile.passwordChangedDescription": "您的密码已成功更改。", "profile.passwordChangeError": "密码更改失败", "profile.passwordChangeErrorDescription": "更改密码时出现错误。请再试一次。", "delete": "删除", "profile.deleteAccount": "删除账号", "profile.deleteAccountConfirmation": "您确定要删除您的账户吗？此操作无法撤销，您的所有数据将被永久丢失。", "profile.accountDeleted": "账户已删除", "profile.accountDeletedDescription": "您的账户已成功删除。", "profile.accountDeletionError": "账户删除失败", "profile.accountDeletionErrorDescription": "删除您的账户时发生错误。请再试一次。", "To celebrate our launch, enjoy 50% off select Gemini API models. Offer valid until further notice.": "为庆祝我们的发布，部分Gemini API型号享受五折优惠。优惠有效期至另行通知。", "Check now": "立即查看", "payment.success.title": "付款成功！", "payment.success.message": "感谢您的购买！您的付款已成功处理。", "payment.success.orderId": "订单编号：", "payment.success.redirecting": "在 {seconds} 秒内重定向到您的订单…", "payment.success.viewOrders": "查看我的订单", "payment.error.title": "支付错误", "payment.error.message": "处理您的付款时出现问题。如果这种情况持续发生，请联系支持。", "payment.error.backToOrders": "返回订单列表", "Overview of your credits status.": "您的积分状态概览。", "Payment History": "付款记录", "Your payment history will appear here once you have made a purchase.": "您的付款记录将在您完成购买后显示在此处。", "Payment method": "付款方式", "Purchase Date": "购买日期", "Amount": "数量", "Status": "状态", "Payment amount": "付款金额", "payment.status.unavailable": "不可用", "payment.status.created": "创建", "payment.status.completed": "完成", "payment.status.failed": "失败", "payment.status.canceled": "取消", "payment.status.processing": "处理中", "payment.status.refund": "退款", "payment.status.partial_paid": "部分支付", "apiKeys.successTitle": "API密钥创建成功", "apiKeys.importantNotice": "重要通知", "apiKeys.copyWarning": "这是您唯一一次可以查看和复制此API密钥的机会。请立即复制并安全存储。", "apiKeys.key": "API密钥", "apiKeys.securityTip": "安全提示：", "apiKeys.tip1": "将此密钥存储在安全的位置。", "apiKeys.tip2": "永远不要公开分享您的 API 密钥", "apiKeys.tip3": "如果遭到泄露，删除此密钥并创建一个新的。", "apiKeys.copyFirst": "首先复制API密钥", "common.done": "完成", "Integration": "融合", "API Keys": "API密钥", "Manage your API keys.": "管理您的API密钥。", "Create API Key": "创建API密钥", "Name your API key.": "命名你的API密钥。", "Create": "创建", "You have not created any API keys yet.": "您尚未创建任何API密钥。", "Copy API Key": "复制 API 密钥", "Delete API Key": "删除 API 密钥", "EMAIL_NOT_EXIST": "电子邮件不存在", "Your account is not verified": "您的账户尚未验证", "Your account is not verified. Please verify your account to continue": "您的账户尚未验证。请验证您的账户以继续。", "TOKEN_USED": "令牌已被使用", "NOT_ENOUGH_CREDIT": "余额不足。请充值。", "Not enough credit": "信用不足", "Your account does not have enough credit. Please top up your account to continue.": "您的账户余额不足。请充值以继续使用。", "{n} credits": "{n}学分", "USD / {unit}": "美元 / {unit}", "Credits / {unit}": "学分 / {unit}", "Save {n}%": "节省{n}%", "${price} = {n} credits": "${price} = {n}学分", "You can switch between money and credits to see the price in your preferred currency.": "您可以在金钱和积分之间切换，以查看您首选货币的价格。", "Forever": "永远", "For large organizations.": "对于大型组织。", "Free": "免费", "Contact us": "联系我们", "Contact sales": "联系销售", "Premium": "优质", "Enterprise": "企业", "Show money": "炫富", "Show credits": "显示致谢", "Auto upgrade after buy credits": "购买积分后自动升级", "Image": "图像", "Video": "视频", "Audio": "音频", "Dialog": "对话", "Get started": "开始", "Contact": "联系", "Image Style": "图像风格", "Image Aspect Ratio": "图像纵横比", "Enhance Prompt": "增强提示", "Aspect Ratio": "纵横比", "Support multiple aspect ratio": "支持多种纵横比", "Support enhance prompt": "支持增强提示", "Up to {size}MB": "最多{size}MB", "Budget Calculator": "预算计算器", "Resource Calculator": "资源计算器", "Budget Amount": "预算金额", "Resources you can generate:": "您可以生成的资源：", "Select resources you want:": "选择您想要的资源：", "credits": "积分", "image": "图像", "video": "视频", "per item": "每件物品", "Quantity": "数量", "Total Cost:": "总成本：", "Approximately {credits} credits": "大约 {credits} 学分", "Images": "图像", "Videos": "视频", "Pricing Calculator": "定价计算器", "Calculate how many resources can you generate with your budget.": "计算在您的预算范围内可以产生多少资源。", "Minimum $10 required": "最低需$10", "Minimum Purchase Required": "最低消费要求", "Minimum purchase amount is $10. Please increase your selection.": "最低消费金额为$10。请增加您的选择。", "Minimum purchase amount is $10": "最低购买金额为10美元。", "Please add more resources to reach the minimum purchase amount.": "请增加资源以达到最低购买金额。", "Enter exact number": "输入确切数字", "Enter budget amount": "输入预算金额", "Min: $10": "最低：$10", "Each amount shows what you can generate with your entire budget (choose one type)": "每个金额显示您可以用您的全部预算生成的内容（选择一种类型）", "OR": "或", "AI Image Generation Examples": "人工智能图像生成示例", "Explore the power of AI image generation with these interactive comparisons": "探索AI图像生成的强大功能，通过这些互动对比展示。", "Try the Comparison!": "试试比较吧！", "Drag the slider left and right to compare the before and after images. You can also click anywhere on the image to move the slider.": "拖动滑块左右滑动以比较前后图像。您也可以单击图像上的任意位置来移动滑块。", "Got it!": "明白了！", "Please login to access your saved prompts.": "请登录以访问您保存的提示。", "Access Your Personal Voices": "访问您的个人语音", "Access Your Favorite Voices": "访问您喜爱的声音", "Login to view and manage your personal voice collection. Upload custom voices and access them anytime.": "登录以查看和管理您的个人语音收藏。上传自定义语音并随时访问它们。", "Login to view your favorite voices. Save voices you love and access them quickly for your projects.": "登录以查看您喜欢的声音。保存您喜爱的声音，便于快速访问和使用于您的项目。", "Create Account": "创建账户", "Join thousands of creators using AI voices for their projects": "成千上万的创作者正在为他们的项目使用AI语音。", "AI Video Generation Examples": "人工智能视频生成示例", "Explore the power of AI video generation with these interactive comparisons": "探索通过这些互动对比生成AI视频的强大功能", "Try the Video Comparison!": "试试视频比较！", "Drag the slider left and right to compare text prompts/images with generated videos. You can also click anywhere to move the slider.": "将滑块左右拖动以比较文本提示/图像与生成的视频。您也可以点击任意位置来移动滑块。", "Duration": "持续时间", "Select video duration in seconds": "选择视频时长（秒）", "This setting is locked for the selected model": "此设置已锁定，无法用于所选模型。", "Prompts will always be refined to improve output quality (required for this model)": "提示将始终被优化以提高输出质量（此模型所需）。", "SIGNUP_MAIL_EXIST": "电子邮件已存在", "SIGNIN_USER_NOT_FOUND": "未找到用户", "SIGNIN_USER_NOT_VERIFIED": "用户未验证", "SIGNIN_USER_DISABLED": "用户已禁用", "SIGNIN_WRONG_PASSWORD": "密码错误", "SIGNIN_USER_NOT_FOUND_FOR_EMAIL": "未找到该电子邮件的用户", "SIGNIN_INVALID_EMAIL": "无效的电子邮件", "auth.accountCreated": "账户已创建", "auth.accountCreatedDescription": "您的账户已成功创建。请检查您的电子邮件以验证您的账户。", "Select voice on right": "在右侧选择语音", "privacy.lastUpdated": "最后更新：", "privacy.lastUpdatedDate": "2025年1月15日", "privacy.introduction": "在GeminiGen.AI，我们优先保护您的隐私和个人信息的安全。此隐私政策概述了我们在使用我们的AI内容生成服务（包括图像生成、视频生成、语音合成和对话生成）时如何收集、利用和保护您提供的信息。通过访问和使用我们的网站（geminigen.ai），您即表示同意本政策中所述的做法。", "privacy.informationCollectionDescription": "当您在我们的网站上创建帐户时，我们会收集某些个人信息，例如您的电子邮件地址和完整姓名。此信息对于为您提供服务、提供服务更新或变更以及进行统计分析以提升我们的服务非常必要。此外，为AI内容生成而上传的任何文本、图像或文档都只是暂时存储，以便生成输出。", "privacy.creditCalculation": "2. 信用计算", "privacy.creditCalculationDescription": "为了确保准确计费，生成AI内容所需的积分数量是根据所提供的文本、图像或文档进行计算的。此计算使用我们的专有算法完成，并与输入的复杂性和长度成正比。", "privacy.paymentSecurity": "3. 付款与安全性", "privacy.paymentSecurityDescription": "关于支付处理，我们提供PayPal和信用卡选项。我们不会在服务器上存储信用卡信息。所有支付交易均由值得信赖的第三方支付服务提供商安全处理，符合各自的隐私和安全政策。", "privacy.emailNotification": "4. 电子邮件通知和访问生成的内容", "privacy.emailNotificationDescription": "内容生成完成后，您将收到一封电子邮件通知，其中包含一个安全链接，以便访问和下载生成的输出（图像、视频、音频文件或对话）。为方便起见，该链接在指定时间段内保持有效。", "privacy.thirdPartyServices": "6. 第三方服务", "privacy.thirdPartyServicesDescription": "我们可能会使用第三方服务，例如分析提供商，以提升我们的服务并分析使用模式。这些服务可能会收集有关您使用情况的信息，但无法获取您的个人信息。", "privacy.cookies": "7. <PERSON><PERSON>和追踪技术", "privacy.cookiesDescription": "我们的网站使用cookies和类似的跟踪技术来提高用户体验和分析网站使用情况。您可以通过浏览器设置禁用cookies，但请注意，这可能会导致我们网站的一些功能无法正常运行。", "privacy.thirdPartyLinks": "8. 第三方链接", "privacy.thirdPartyLinksDescription": "我们的网站可能包含指向第三方网站的链接。我们对这些网站的隐私惯例或内容不承担责任，并鼓励您查看其各自的隐私政策。", "privacy.childrenPrivacy": "9. 儿童隐私权", "privacy.childrenPrivacyDescription": "我们的服务不适用于18岁以下的个人，我们不会故意收集或存储18岁以下人士的个人信息。如果我们发现无意中收集了18岁以下儿童的个人信息，我们将采取措施从我们的记录中删除这些信息。", "privacy.policyChanges": "10. 我们的隐私政策更新", "privacy.policyChangesDescription": "我们可能会定期更新我们的隐私政策，以反映实践或法律要求的变化。任何修订将在我们的网站上发布更新政策后立即生效。我们鼓励您定期查看此隐私政策以获取最新信息。", "privacy.commercialUse": "11. 商业用途", "privacy.commercialUseDescription": "您有权将我们的服务生成的内容用于个人、教育或商业目的。但是，未经GeminiGen.AI的事先书面同意，您不得转售、重新分发或再授权该生成内容。", "privacy.otherPeoplePrivacy": "12. 他人的隐私", "privacy.otherPeoplePrivacyDescription": "使用我们的服务时，您必须尊重他人的隐私。未经许可，不得上传或创建包含个人信息、机密数据或受版权保护材料的内容。", "privacy.unsubscribe": "13. 取消订阅", "privacy.unsubscribeDescription": "您可以通过点击个人资料设置中的“切换”按钮选择不参与定向广告。", "terms.lastUpdated": "最后更新：", "terms.lastUpdatedDate": "2025年1月15日", "terms.introduction": "欢迎使用GeminiGen.AI。这些服务条款规范您对我们AI驱动内容生成服务的使用，包括图像生成、视频生成、语音合成和对话生成。", "terms.acceptanceOfTermsDetails": "您继续使用我们的服务即表示接受这些条款的任何更改。", "terms.serviceDescription": "服务描述", "terms.serviceDescriptionText": "双子生成.AI提供基于人工智能的内容生成服务，包括但不限于：", "terms.serviceUsageDescription": "您承诺仅为合法目的使用我们的服务。您应避免上传、传输或存储任何非法、有害、诽谤或侵犯他人权利的内容。您对提交用于AI内容生成的任何内容负全责。", "terms.permittedUse": "4.1 允许使用", "terms.permitted1": "使用我们的服务用于合法、创意和商业目的。", "terms.permitted2": "生成符合适用法律法规的内容。", "terms.permitted3": "尊重他人的知识产权。", "terms.permitted4": "根据我们的许可条款使用生成的内容。", "terms.prohibitedUse": "4.2 禁止使用", "terms.prohibited1": "生成非法、有害、威胁、辱骂或歧视性的内容。", "terms.prohibited2": "制作侵犯他人知识产权的内容", "terms.prohibited3": "制作旨在欺骗、诈骗或误导他人的内容", "terms.prohibited4": "生成描绘未成年人处于不当情境的内容。", "terms.prohibited5": "制作宣传暴力、恐怖主义或非法活动的内容", "terms.prohibited6": "使用我们的服务来发送垃圾信息、骚扰或伤害他人", "terms.prohibited7": "尝试逆向工程、破解或攻击我们的系统", "terms.prohibited8": "违反任何适用的法律或法规", "terms.userAccounts1": "提供准确完整的注册信息", "terms.userAccounts2": "维护您的账户凭证的安全和机密性", "terms.userAccounts3": "在您的账户下发生的所有活动", "terms.userAccounts4": "立即通知我们您的账户被未经授权使用。", "terms.userAccounts5": "确保您的账户信息保持最新和准确。", "terms.paymentAndBilling": "3. 付款与信用", "terms.paymentAndBillingDescription": "我们的人工智能内容生成服务基于积分系统运行。内容创作所需的积分数量由我们的专有算法确定，并根据输入的复杂性和输出要求进行精确计算。", "terms.payment1": "账户余额用尽后，您必须充值。", "terms.payment2": "可通过PayPal或信用卡进行付款。", "terms.payment3": "所有付款均通过第三方支付处理器安全处理。", "terms.payment4": "学分不可退还，除非适用法律要求。", "terms.payment5": "价格如有变动，将提前合理通知。", "terms.payment6": "您需承担所有适用的税费。", "terms.ourIntellectualProperty": "5.1 我们的知识产权", "terms.ourIntellectualPropertyDescription": "GeminiGen.AI及其服务，包括所有软件、算法、设计和内容，受知识产权法保护。未经我们的书面许可，您不得复制、修改、分发或创作衍生作品。", "terms.userGeneratedContent": "5.2 用户生成内容", "terms.userGeneratedContentDescription": "您保留使用我们服务创建的内容的所有权，但需符合以下条件：", "terms.userContent1": "您授予我们有限的许可，以处理和存储您的内容来提供我们的服务。", "terms.userContent2": "您声明您有权使用您提供的任何输入内容。", "terms.userContent3": "您有责任确保您生成的内容符合这些条款。", "terms.userContent4": "我们可能会移除违反我们政策或适用法律的内容。", "terms.privacyAndDataProtection": "7. 隐私和数据保护", "terms.privacyAndDataProtectionDescription": "您的隐私对我们很重要。我们对您的个人信息的收集、使用和保护受我们的隐私政策的约束，该政策通过引用并入这些条款。", "terms.serviceAvailability": "8. 服务可用性", "terms.serviceAvailabilityDescription": "虽然我们努力提供可靠的服务，但我们不保证不间断的访问。由于维护、更新或技术问题，我们的服务可能会暂时不可用。我们保留在合理通知的情况下修改或中断服务的权利。", "terms.terminationByUser": "8.1 您的终止权利", "terms.terminationByUserDescription": "您可以随时通过联系我们的支持团队来终止您的账户。账户终止后，您将无法访问我们的服务，但这些条款仍将适用于您之前使用我们的服务。", "terms.terminationByUs": "8.2 我们的终止权利", "terms.terminationByUsDescription": "我们可能会因以下任何原因立即暂停或终止您的账户和服务访问，且无需事先通知或可以在未提前通知的情况下进行：", "terms.termination1": "违反这些服务条款", "terms.termination2": "欺诈、滥用或非法活动", "terms.termination3": "未支付费用或收费", "terms.termination4": "长时间不活动", "terms.termination5": "法律或监管要求", "terms.termination6": "保护我们的权利、财产或安全", "terms.limitationOfLiability": "第六条 责任限制", "terms.limitationOfLiabilityDescription": "GeminiGen.AI 对因您使用我们的服务而引起或与之相关的任何直接、间接、附带、特殊或后果性损害不承担责任。我们不保证服务的准确性、完整性或可用性，并且声明不承担任何关于其使用或结果的明示或暗示的保证。", "terms.disclaimer1": "适销性担保、特定用途适用性担保和非侵权担保", "terms.disclaimer2": "关于生成内容的准确性、可靠性或质量的保证", "terms.disclaimer3": "如何使用或分发生成内容的责任", "terms.disclaimer4": "因服务中断或技术问题造成的任何损害的责任", "terms.indemnification": "12. 赔偿条款", "terms.indemnificationDescription": "您同意赔偿、为GeminiGen.AI及其关联公司辩护，并使其免受因您使用我们的服务、违反这些条款或侵犯任何第三方权利而产生的任何索赔、损害赔偿、损失或费用。", "terms.governingLaw": "9. 适用法律", "terms.governingLawDescription": "本服务条款应根据越南法律进行解释和适用，不考虑其法律冲突原则。因本条款和使用我们的服务而引起的任何争议应由越南法院专属管辖。", "terms.clarificationOpenAI": "关于第三方人工智能服务的说明", "terms.clarificationOpenAIDescription": "GeminiGen.AI是一个独立实体，与OpenAI、谷歌或其他AI服务提供商没有关联。我们的内容生成服务利用各种AI API将文本转换为图像、视频、语音和对话，但我们独立于这些提供商运营。这一声明是为了防止有关GeminiGen.AI与第三方AI服务提供商之间关系的任何混淆或误解。用户应该了解，尽管我们使用第三方AI技术来提供服务，但GeminiGen.AI对服务的运营以及对这些服务条款的遵守负有唯一责任。", "terms.contactEmail": "电子邮件：", "terms.contactAddress": "地址：", "terms.companyAddress": "双子座人工智能组织地址：美国爱荷华州得梅因市鲍登街3100号，邮编50313", "second": "第二个", "1M characters": "100万字符", "Support {n}+ voices": "支持 {n}+ 个声音", "Support {n}+ emotions": "支持{n}+情感", "Support {n}+ languages": "支持{n}+种语言", "Support custom prompt": "支持自定义提示", "Support MP3 and WAV": "支持MP3和WAV", "Support speed control": "支持速度控制", "Support document to speech": "支持演讲的文件", "Current plan": "当前计划", "Already premium": "已经是高级会员", "Gemini 2.5 Flash": "双子座2.5闪光版", "Gemini 2.5 Pro": "双子星2.5 Pro", "Voice": "语音", "Emotion": "情感", "Language": "语言", "Output Format": "输出格式", "Speed": "速度", "Document": "文件", "Enjoy 50% off select Gemini API models. Offer valid until further notice.": "部分Gemini API模型享受五折优惠。优惠有效期至另行通知。", "Text must be at least 4 characters long.": "文本长度必须至少为4个字符。", "Each dialog text must be at least 4 characters long.": "每个对话文本必须至少有4个字符。", "Please enter text or select a file to generate speech.": "请输入文本或选择文件以生成语音。", "Please select a voice for speech generation.": "请选择一个用于语音生成的声音。", "Please add at least one dialog to generate speech.": "请添加至少一个对话生成语音。", "Please select voices for both speakers.": "请为双方选择语音。", "Photorealistic": "照片级写实", "imageStyles.photorealistic.description": "高细节和高分辨率的真实图像", "Delete Voice": "删除语音", "Are you sure you want to delete this voice? This action cannot be undone.": "您确定要删除此语音吗？此操作无法撤销。", "Voice deleted successfully": "语音删除成功", "Failed to delete voice": "删除语音失败", "Create your first voice": "创建您的第一个语音", "Create Custom Voice": "创建自定义语音", "Type of voice to create": "要创建的语音类型", "Instant Voice Cloning": "即时语音克隆", "Clone a voice from a clean sample recording. Samples should contain 1 speaker and be over 1 minute long and not contain background noise": "从干净的样本录音中克隆声音。样本应包含1名说话者，长度超过1分钟且不含背景噪音。", "Professional Voice Cloning": "专业语音克隆", "Create the most realistic digital replica of your voice.": "创建你的声音的最逼真数字复制品。", "Speaker Name": "演讲者姓名", "Enter speaker name": "输入演讲者姓名", "Describe the voice characteristics": "描述声音特征", "Select gender": "选择性别", "Select age": "选择年龄", "Select accent": "选择口音", "Audio Sample": "音频样本", "I agree to the privacy policy": "我同意隐私政策", "Note:": "注意：", "The sound should be clear, without any noise, and last around 1 minutes to ensure good quality.": "声音应清晰无噪音，并持续约1分钟以确保良好的质量。", "It will cost 0 credits each time you create a voice.": "每次创建语音将花费0个积分。", "Young": "年轻的", "Middle": "中间", "Old": "旧的", "English": "英语", "Custom voice created successfully": "自定义语音创建成功", "Failed to create custom voice": "创建自定义语音失败", "validation.mustAgreeToPrivacy": "您必须同意隐私政策。", "I agree to the {0}": "我同意{0}", "Privacy Policy": "隐私政策", "The sound should be clear, without any noise, and last around 10 minutes to ensure good quality.": "声音应该清晰，无任何噪音，持续约10分钟以确保良好质量。", "It will cost 5,000 credits each time you create a voice.": "每次创建语音都会花费5000积分。", "Maximum file size": "最大文件大小", "File size exceeds maximum limit of {maxSize}": "文件大小超过最大限制{maxSize}", "File size exceeds 150MB limit": "文件大小超过150MB限制", "ui.errors.viewGooglePolicy": "查看谷歌生成式AI使用政策", "negativePrompt": "Negative Prompt", "negativePromptDescription": "Describe what you don't want to see in the video", "negativePromptTooltip": "Negative prompts help exclude unwanted elements, styles, or concepts from your video generation. For example: 'blurry, low quality, distorted faces'", "negativePromptPlaceholder": "Enter what you want to avoid in the video...", "negativePromptSuggestions": "Quick suggestions", "negativePromptSuggestion1": "blurry, low quality", "negativePromptSuggestion2": "distorted faces, deformed", "negativePromptSuggestion3": "text, watermark", "negativePromptSuggestion4": "dark, underexposed", "negativePromptSuggestion5": "shaky, unstable motion", "negativePromptSuggestion6": "pixelated, artifacts"}